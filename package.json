{"name": "influencer-platform-prototype", "version": "2.0.0", "description": "网红建联平台原型 - 性能优化版", "main": "start-server.js", "scripts": {"start": "node start-server.js", "dev": "node start-server.js", "test": "node start-server.js", "serve": "python -m http.server 8000", "performance": "node -e \"console.log('🚀 启动性能测试...'); require('child_process').exec('node start-server.js', (err, stdout, stderr) => { if (err) console.error(err); console.log(stdout); }); setTimeout(() => { require('child_process').exec('open http://localhost:8000/test || start http://localhost:8000/test || xdg-open http://localhost:8000/test'); }, 2000);\""}, "keywords": ["influencer", "platform", "prototype", "font-awesome", "tailwind-css", "performance", "optimization"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=12.0.0"}, "devDependencies": {}, "dependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/yourusername/influencer-platform-prototype.git"}, "bugs": {"url": "https://github.com/yourusername/influencer-platform-prototype/issues"}, "homepage": "https://github.com/yourusername/influencer-platform-prototype#readme", "performance": {"fontAwesome": "6.5.1", "tailwindCSS": "2.2.9", "cdn": "cdn.staticfile.net", "features": ["资源预加载", "GPU加速", "懒加载", "性能监控", "内存优化", "动画优化"]}}